// Unit tests for OllamaService [TDT][REH]

import { OllamaService } from '../services/ollama.service';
import { defaultOllamaConfig } from '../config/ollama';
import { VideoMetadata, FilterResult } from '../types/analysis';

// Mock fetch for testing [TDT]
global.fetch = jest.fn();

// Selective console suppression for testing [TDT][REH]
const originalLog = console.log;
const originalWarn = console.warn;
const originalError = console.error;

// Expected messages that should be suppressed during successful tests [REH]
const EXPECTED_MESSAGES = [
  'Processing batch',
  '🔍 Analyzing channel:',
  '📹 Found',
  '🤖 Starting smart analysis',
  'Ollama attempt',
  'Failed to analyze channel', // Only suppress if it's part of expected error testing
  'Ollama summarization failed',
  'Ollama analysis failed',
  'Failed to parse',
  'Network error',
  'Timeout',
  '500 Internal Server Error',
  'No JSON found in response'
];

const shouldSuppressMessage = (message: string): boolean => {
  return EXPECTED_MESSAGES.some(expected => 
    message.includes(expected)
  );
};

beforeAll(() => {
  console.log = jest.fn().mockImplementation((...args) => {
    const message = args.join(' ');
    if (!shouldSuppressMessage(message)) {
      originalLog(...args); // Show unexpected messages
    }
  });
  
  console.warn = jest.fn().mockImplementation((...args) => {
    const message = args.join(' ');
    if (!shouldSuppressMessage(message)) {
      originalWarn(...args); // Show unexpected warnings
    }
  });
  
  console.error = jest.fn().mockImplementation((...args) => {
    const message = args.join(' ');
    // Only suppress expected error messages during error testing
    if (!shouldSuppressMessage(message)) {
      originalError(...args); // Show unexpected errors
    }
  });
});

afterAll(() => {
  console.log = originalLog;
  console.warn = originalWarn;
  console.error = originalError;
  
  // Ensure all timers are cleaned up [RM]
  jest.useRealTimers();
});

describe('OllamaService', () => {
  let ollamaService: OllamaService;
  
  const mockMetadata: VideoMetadata = {
    id: 'test123',
    title: 'Test Video',
    description: 'Test description',
    channelId: 'testchannel',
    channelTitle: 'Test Channel',
    publishedAt: '2025-01-01T00:00:00Z',
    duration: '10:00'
  };

  const mockKeywordResult: FilterResult = {
    isTechnical: true,
    matchedKeywords: ['AI', 'machine learning']
  };

  beforeEach(() => {
    ollamaService = new OllamaService(defaultOllamaConfig);
    jest.clearAllMocks();
  });

  describe('summarize', () => {
    it('should successfully summarize technical content', async () => {
      const mockResponse = {
        response: JSON.stringify({
          keywords: ['AI', 'ML', 'Python'],
          category: 'AI'
        })
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await ollamaService.summarize(
        'Machine learning tutorial with Python',
        mockMetadata,
        mockKeywordResult
      );

      expect(result.isTechnical).toBe(true);
      expect(result.method).toBe('keyword-confirmed');
      expect(result.aiSummary).toEqual(['AI', 'ML', 'Python']);
      expect(result.category).toBe('AI');
      expect(result.enhancedBy).toBe('ollama');
    });

    it('should handle malformed JSON response', async () => {
      const mockResponse = {
        response: 'Invalid JSON response'
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await ollamaService.summarize(
        'Machine learning tutorial',
        mockMetadata,
        mockKeywordResult
      );

      expect(result.isTechnical).toBe(true);
      expect(result.method).toBe('keyword-confirmed');
      expect(result.aiSummary).toEqual([]);
      expect(result.category).toBe('Other');
    });

    it('should fallback when Ollama API fails', async () => {
      // Mock all retry attempts to fail [TDT]
      (fetch as jest.Mock)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'));

      const result = await ollamaService.summarize(
        'Machine learning tutorial',
        mockMetadata,
        mockKeywordResult
      );

      expect(result.isTechnical).toBe(true);
      expect(result.method).toBe('keyword-confirmed');
      expect(result.error).toBe('ollama-unavailable');
      expect(result.enhancedBy).toBeUndefined();
    }, 10000);
  });

  describe('analyzeAndSummarize', () => {
    it('should analyze and summarize borderline content', async () => {
      const mockResponse = {
        response: JSON.stringify({
          isTechnical: true,
          confidence: 0.85,
          keywords: ['Programming', 'Tutorial'],
          category: 'Development',
          reasoning: 'Contains programming concepts'
        })
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await ollamaService.analyzeAndSummarize(
        'Programming tutorial discussion',
        mockMetadata
      );

      expect(result.isTechnical).toBe(true);
      expect(result.method).toBe('ollama-analysis');
      expect(result.confidence).toBe(0.85);
      expect(result.aiSummary).toEqual(['Programming', 'Tutorial']);
      expect(result.category).toBe('Development');
      expect(result.reasoning).toBe('Contains programming concepts');
    });

    it('should handle non-technical classification', async () => {
      const mockResponse = {
        response: JSON.stringify({
          isTechnical: false,
          confidence: 0.9,
          keywords: [],
          category: 'Other',
          reasoning: 'Entertainment content'
        })
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await ollamaService.analyzeAndSummarize(
        'Funny cat video compilation',
        mockMetadata
      );

      expect(result.isTechnical).toBe(false);
      expect(result.method).toBe('ollama-analysis');
      expect(result.aiSummary).toEqual([]);
      expect(result.reasoning).toBe('Entertainment content');
    });
  });

  describe('API error handling', () => {
    it('should retry on network failures', async () => {
      (fetch as jest.Mock)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            response: JSON.stringify({
              keywords: ['AI'],
              category: 'AI'
            })
          })
        });

      const result = await ollamaService.summarize(
        'AI tutorial',
        mockMetadata,
        mockKeywordResult
      );

      expect(fetch).toHaveBeenCalledTimes(3);
      expect(result.isTechnical).toBe(true);
      expect(result.enhancedBy).toBe('ollama');
    }, 10000);

    it('should handle HTTP errors', async () => {
      // Mock all retry attempts to return HTTP errors [TDT]
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error'
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error'
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error'
        });

      const result = await ollamaService.summarize(
        'AI tutorial',
        mockMetadata,
        mockKeywordResult
      );

      expect(result.error).toBe('ollama-unavailable');
    }, 10000);

    it('should handle timeout', () => {
      // Disable fake timers for this test to avoid issues [RM]
      jest.useRealTimers();
      
      // Mock the fetch implementation to immediately reject with Timeout
      (fetch as jest.Mock)
        .mockRejectedValueOnce(new Error('Timeout'))
        .mockRejectedValueOnce(new Error('Timeout'))
        .mockRejectedValueOnce(new Error('Timeout'));

      // Return a resolved promise with the test
      return ollamaService.summarize(
        'AI tutorial',
        mockMetadata,
        mockKeywordResult
      ).then(result => {
        expect(result.error).toBe('ollama-unavailable');
      });
    });
    

    it('should normalize category variations', () => {
      // Use a synchronous approach for category validation [TDT][PA]
      const testCases = [
        { input: 'ai', expected: 'AI' },
        { input: 'development', expected: 'Development' },
        { input: 'AGILE', expected: 'Agile' },
        { input: 'testing', expected: 'Testing' },
        { input: 'unknown', expected: 'Other' }
      ];

      // Setup all mocks in advance
      const mockResponses = testCases.map(testCase => ({
        ok: true,
        json: () => Promise.resolve({
          response: JSON.stringify({
            keywords: ['test'],
            category: testCase.input
          })
        })
      }));

      (fetch as jest.Mock).mockImplementation(() => {
        const response = mockResponses.shift();
        return Promise.resolve(response);
      });

      // Chain promises for each test case
      return testCases.reduce((promise, testCase) => {
        return promise.then(() => {
          return ollamaService.summarize('test content', mockMetadata, mockKeywordResult)
            .then(result => {
              expect(result.category).toBe(testCase.expected);
            });
        });
      }, Promise.resolve());
    });
  });

  describe('context building', () => {
    it('should build proper context for analysis', () => {
      // Simplify test to avoid timeout issues [TDT][PA]
      const mockResponse = {
        response: JSON.stringify({
          keywords: ['AI'],
          category: 'AI'
        })
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      // Return a promise instead of using async/await
      return ollamaService.summarize(
        'Long content that should be truncated after 500 characters. '.repeat(20),
        mockMetadata,
        mockKeywordResult
      ).then(() => {
        const fetchCall = (fetch as jest.Mock).mock.calls[0];
        const requestBody = JSON.parse(fetchCall[1].body);
        
        expect(requestBody.prompt).toContain('Title: Test Video');
        expect(requestBody.prompt).toContain('Channel: Test Channel');
        expect(requestBody.prompt).toContain('Duration: 10:00');
        expect(requestBody.prompt).toContain('Published: 2025-01-01T00:00:00Z');
      });
    });
  });
});
